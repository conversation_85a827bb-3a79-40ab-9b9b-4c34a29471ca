Metadata-Version: 2.4
Name: firebird-driver
Version: 2.0.2
Summary: Firebird driver for Python
Project-URL: Home, https://github.com/FirebirdSQL/python3-driver
Project-URL: Documentation, https://firebird-driver.rtfd.io
Project-URL: Issues, https://github.com/FirebirdSQL/python3-driver/issues
Project-URL: Funding, https://github.com/sponsors/pcisar
Project-URL: Source, https://github.com/FirebirdSQL/python3-driver
Author-email: <PERSON> <<EMAIL>>
License: MIT License
        
        Copyright (c) 2020 The Firebird Project (www.firebirdsql.org)
        
        Permission is hereby granted, free of charge, to any person obtaining a copy
        of this software and associated documentation files (the "Software"), to deal
        in the Software without restriction, including without limitation the rights
        to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
        copies of the Software, and to permit persons to whom the Software is
        furnished to do so, subject to the following conditions:
        
        The above copyright notice and this permission notice shall be included in all
        copies or substantial portions of the Software.
        
        THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
        IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
        FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
        AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
        LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
        OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
        SOFTWARE.
License-File: LICENSE
Keywords: Firebird,RDBMS,driver
Classifier: Development Status :: 5 - Production/Stable
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: MIT License
Classifier: Operating System :: MacOS
Classifier: Operating System :: Microsoft :: Windows
Classifier: Operating System :: POSIX :: Linux
Classifier: Programming Language :: Python :: 3 :: Only
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Classifier: Topic :: Database
Classifier: Topic :: Software Development
Requires-Python: >=3.11
Requires-Dist: firebird-base~=2.0
Requires-Dist: python-dateutil~=2.8
Description-Content-Type: text/markdown

# firebird-driver

## Firebird driver for Python

[![PyPI - Version](https://img.shields.io/pypi/v/firebird-driver.svg)](https://pypi.org/project/firebird-driver)
[![PyPI - Python Version](https://img.shields.io/pypi/pyversions/firebird-driver.svg)](https://pypi.org/project/firebird-driver)
[![Hatch project](https://img.shields.io/badge/%F0%9F%A5%9A-Hatch-4051b5.svg)](https://github.com/pypa/hatch)
[![PyPI - Downloads](https://img.shields.io/pypi/dm/firebird-driver)](https://pypi.org/project/firebird-driver)
[![Libraries.io SourceRank](https://img.shields.io/librariesio/sourcerank/pypi/firebird-driver)](https://libraries.io/pypi/firebird-driver)

This package provides official Python Database API 2.0-compliant driver for the open
source relational database Firebird®. In addition to the minimal feature set of
the standard Python DB API, this driver also exposes the new (interface-based)
client API introduced in Firebird 3, and number of additional extensions and
enhancements for convenient use of Firebird RDBMS.

-----

**Table of Contents**

- [Installation](#installation)
- [License](#license)
- [Documentation](#documentation)

## Installation

Requires: Firebird 3+

```console
pip install firebird-driver
```
See [firebird-lib](https://pypi.org/project/firebird-lib/) package for optional extensions
to this driver.

## License

`firebird-driver` is distributed under the terms of the [MIT](https://spdx.org/licenses/MIT.html) license.

## Documentation

The documentation for this package is available at [https://firebird-driver.readthedocs.io](https://firebird-driver.readthedocs.io)

## Running tests

This project uses [hatch](https://hatch.pypa.io/latest/) , so you can use:
```console
hatch test
```
to run all tests for default Python version (3.11). To run tests for all Python versions
defined in matrix, use `-a` switch.

This project is using [pytest](https://docs.pytest.org/en/stable/) for testing, and our
tests add several options via `tests/conftest.py`.

By default, tests are configured to use local Firebird installation via network access.
To use local installation in `embedded` mode, comment out the section:
```
[tool.hatch.envs.hatch-test]
extra-args = ["--host=localhost"]
```
in `pyproject.toml`.

You can also use firebird driver configuration file to specify server(s) that should be
used for testing, and then pass `--driver-config` and `--server` options to `pytest`.
