import pandas as pd
import pymysql
import firebird.driver as fdb
from datetime import datetime

def month_category(month):
    if month in ['01', '04', '07', '10']:
        return 1
    elif month in ['02', '05', '08', '11']:
        return 2
    else:
        return 3

def connect_firebird(host, database, fb_library_path=None):
    """Connect to Firebird database"""
    # Set the library path if provided
    if fb_library_path:
        fdb.driver_config.fb_client_library.value = fb_library_path
    
    dsn = f"{host}:{database}"
    return fdb.connect(
        database=dsn,
        user='SYSDBA',
        password='masterkey'
    )

def get_firebird_data(connection, query):
    """Execute query on Firebird and return DataFrame"""
    return pd.read_sql(query, connection)

def main():
    # Get current month for filtering
    current_month = datetime.now().strftime('%m')
    thismonth = month_category(current_month)
    
    # Firebird connections with library path
    fb_lib_path = r"C:\Program Files\Firebird\Firebird_3_0\bin\fbclient.dll"
    tam_conn = connect_firebird('*************', '/data/Taiwan/db2000/HHM8.gdb', fb_lib_path)
    mod_conn = connect_firebird('*************', '/data/CHT/db2000/HHM8.gdb', fb_lib_path)
    
    # Query Firebird databases
    psql = "select panel, surname, citysize from fam where status <>'099'"
    
    tam_df = get_firebird_data(tam_conn, psql)
    tam_df['source'] = 'TAM'
    
    mod_df = get_firebird_data(mod_conn, psql)
    mod_df['source'] = 'MOD'
    
    # Merge Firebird data
    firebird_df = pd.concat([tam_df, mod_df], ignore_index=True)
    
    # MySQL connection
    mysql_conn = pymysql.connect(
        host="*************",
        user="root",
        password="22318430",
        database="order"
    )
    
    # Execute MySQL queries
    sql1 = f"select A.regioncode from mainarea A,maintain B where B.mainname='Sue' and A.maintainer=B.maincode and A.month1={thismonth}"
    sql2 = "select * from torder where eng ='MAIN-順查' and F_date is null"
    
    mysql_df1 = pd.read_sql(sql1, mysql_conn)
    mysql_df2 = pd.read_sql(sql2, mysql_conn)
    
    # Filter: match citysize with regioncode
    filtered_df = firebird_df[firebird_df['citysize'].isin(mysql_df1['regioncode'])]
    
    # Export to Excel
    with pd.ExcelWriter('recruitor_report.xlsx') as writer:
        filtered_df.to_excel(writer, sheet_name='Filtered_Data', index=False)
        mysql_df2.to_excel(writer, sheet_name='TOrder_Data', index=False)
        firebird_df.to_excel(writer, sheet_name='All_Firebird_Data', index=False)
    
    # Close connections
    tam_conn.close()
    mod_conn.close()
    mysql_conn.close()
    
    print("Report exported to recruitor_report.xlsx")

if __name__ == "__main__":
    main()




