import pandas as pd
import pymysql
import firebird.driver as fdb
from datetime import datetime

def month_category(month):
    if month in ['01', '04', '07', '10']:
        return 1
    elif month in ['02', '05', '08', '11']:
        return 2
    else:
        return 3

def connect_firebird(host, database, fb_library_path=None):
    """Connect to Firebird database"""
    # Set the library path if provided
    if fb_library_path:
        fdb.driver_config.fb_client_library.value = fb_library_path
    
    dsn = f"{host}:{database}"
    return fdb.connect(
        database=dsn,
        user='SYSDBA',
        password='masterkey'
    )

def get_firebird_data(connection, query):
    """Execute query on Firebird and return DataFrame"""
    return pd.read_sql(query, connection)

def main():
    # Get current month for filtering
    current_month = datetime.now().strftime('%m')
    thismonth = month_category(current_month)

    firebird_df = None
    mysql_df1 = None
    mysql_df2 = None

    try:
        # Firebird connections with library path
        fb_lib_path = r"C:\Program Files\Firebird\Firebird_3_0\bin\fbclient.dll"
        print("Attempting to connect to Firebird databases...")
        tam_conn = connect_firebird('*************', '/data/Taiwan/db2000/HHM8.gdb', fb_lib_path)
        mod_conn = connect_firebird('*************', '/data/CHT/db2000/HHM8.gdb', fb_lib_path)

        # Query Firebird databases - only panels starting with '123'
        psql_tam = "select panel, surname, citysize from fam where status <>'099'"
        
        psql_mod ="select panel, surname, citysize from fam where status <>'099' and panel like '123%'" 

        tam_df = get_firebird_data(tam_conn, psql_tam)
        tam_df['source'] = 'TAM'

        mod_df = get_firebird_data(mod_conn, psql_mod)
        mod_df['source'] = 'MOD'

        # Merge Firebird data
        firebird_df = pd.concat([tam_df, mod_df], ignore_index=True)
        print(firebird_df.head())

        # Format PANEL column to 10 digits (pad with zeros)
        firebird_df['PANEL'] = firebird_df['PANEL'].astype(str).str.zfill(10)

        # Format CitySize column to 4 digits (pad with zeros)
        firebird_df['CITYSIZE'] = firebird_df['CITYSIZE'].astype(str).str.zfill(4)

        print(firebird_df.head())

        firebird_df.to_excel('firebird_data.xlsx', index=False)
        print(f"Successfully retrieved {len(firebird_df)} records from Firebird databases")

        # Close Firebird connections
        tam_conn.close()
        mod_conn.close()

    except Exception as e:
        print(f"Firebird connection failed: {e}")
        print("Creating sample Firebird data for testing...")
        # Create sample data for testing
        firebird_df = pd.DataFrame({
            'panel': ['1001', '1002', '1003', '1004', '1005'],
            'surname': ['Smith', 'Johnson', 'Williams', 'Brown', 'Jones'],
            'citysize': ['1', '2', '3', '1', '2'],
            'source': ['TAM', 'TAM', 'MOD', 'MOD', 'TAM']
        })

        # Format PANEL column to 10 digits (pad with zeros)
        firebird_df['panel'] = firebird_df['panel'].astype(str).str.zfill(10)

        # Format CitySize column to 4 digits (pad with zeros)
        firebird_df['citysize'] = firebird_df['citysize'].astype(str).str.zfill(4)

        print(f"Created sample data with {len(firebird_df)} records")

    try:
        # MySQL connection
        print("Attempting to connect to MySQL database...")
        mysql_conn = pymysql.connect(
            host="*************",
            user="root",
            password="22318430",
            database="order"
        )

        # Execute MySQL queries
        sql1 = f"select A.regioncode from mainarea A,maintain B where B.mainname='Sue' and A.maintainer=B.maincode and A.month1={thismonth}"
        sql2 = "select * from torder where eng ='MAIN-順查' and F_date is null"

        mysql_df1 = pd.read_sql(sql1, mysql_conn)
        mysql_df2 = pd.read_sql(sql2, mysql_conn)
        print(f"Successfully retrieved MySQL data: {len(mysql_df1)} region codes, {len(mysql_df2)} orders")

        # Close MySQL connection
        mysql_conn.close()

    except Exception as e:
        print(f"MySQL connection failed: {e}")
        print("Creating sample MySQL data for testing...")
        # Create sample data for testing
        mysql_df1 = pd.DataFrame({'regioncode': ['01', '02', '03']})
        mysql_df2 = pd.DataFrame({
            'order_id': ['O001', 'O002'],
            'eng': ['MAIN-順查', 'MAIN-順查'],
            'F_date': [None, None]
        })
        print(f"Created sample MySQL data: {len(mysql_df1)} region codes, {len(mysql_df2)} orders")

    # Filter: match citysize with regioncode
    if firebird_df is not None and mysql_df1 is not None:
        filtered_df = firebird_df[firebird_df['citysize'].isin(mysql_df1['regioncode'])]
        print(f"Filtered data: {len(filtered_df)} records match the criteria")
    else:
        filtered_df = pd.DataFrame()
        print("No data to filter")

    # Export to Excel
    try:
        with pd.ExcelWriter('recruitor_report.xlsx') as writer:
            if not filtered_df.empty:
                filtered_df.to_excel(writer, sheet_name='Filtered_Data', index=False)
            if mysql_df2 is not None and not mysql_df2.empty:
                mysql_df2.to_excel(writer, sheet_name='TOrder_Data', index=False)
            if firebird_df is not None and not firebird_df.empty:
                firebird_df.to_excel(writer, sheet_name='All_Firebird_Data', index=False)

        print("Report exported to recruitor_report.xlsx")

    except Exception as e:
        print(f"Error exporting to Excel: {e}")

if __name__ == "__main__":
    main()




