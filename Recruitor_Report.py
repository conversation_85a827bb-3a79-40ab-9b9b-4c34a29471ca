import pandas as pd
import pymysql
import firebird.driver as fdb
from datetime import datetime
import configparser
import os

def month_category(month):
    if month in ['01', '04', '07', '10']:
        return 1
    elif month in ['02', '05', '08', '11']:
        return 2
    else:
        return 3

def connect_firebird(host, database, fb_library_path=None):
    """Connect to Firebird database"""
    # Set the library path if provided
    if fb_library_path:
        fdb.driver_config.fb_client_library.value = fb_library_path
    
    dsn = f"{host}:{database}"
    return fdb.connect(
        database=dsn,
        user='SYSDBA',
        password='masterkey'
    )

def get_firebird_data(connection, query):
    """Execute query on Firebird and return DataFrame"""
    return pd.read_sql(query, connection)

def main():
    # Read configuration file
    config = configparser.ConfigParser()
    config.read('config.ini')

    # Get output path from config
    try:
        output_path = config.get('EXCEL_REPORT', 'path')
        print(f"Output path from config: {output_path}")

        # Create directory if it doesn't exist
        if not os.path.exists(output_path):
            os.makedirs(output_path)
            print(f"Created directory: {output_path}")
    except Exception as e:
        print(f"Error reading config or creating directory: {e}")
        output_path = "."  # Default to current directory
        print("Using current directory as fallback")

    # Get current month for filtering
    current_month = datetime.now().strftime('%m')
    thismonth = month_category(current_month)

    firebird_df = None
    mysql_df1 = None
    mysql_df2 = None

    try:
        # Firebird connections with library path
        fb_lib_path = r"C:\Program Files\Firebird\Firebird_3_0\bin\fbclient.dll"
        print("Attempting to connect to Firebird databases...")
        tam_conn = connect_firebird('10.128.174.12', '/data/Taiwan/db2000/HHM8.gdb', fb_lib_path)
        mod_conn = connect_firebird('10.128.174.12', '/data/CHT/db2000/HHM8.gdb', fb_lib_path)

        # Query Firebird databases - only panels starting with '123'
        psql_tam = "select panel, surname, citysize from fam where status <>'099'"
        
        psql_mod ="select panel, surname, citysize from fam where status <>'099' and panel like '123%'" 

        tam_df = get_firebird_data(tam_conn, psql_tam)
        tam_df['source'] = 'TAM'

        mod_df = get_firebird_data(mod_conn, psql_mod)
        mod_df['source'] = 'MOD'

        # Merge Firebird data
        firebird_df = pd.concat([tam_df, mod_df], ignore_index=True)
        print(firebird_df.head())

        # Format PANEL column to 10 digits (pad with zeros)
        firebird_df['PANEL'] = firebird_df['PANEL'].astype(str).str.zfill(10)

        # Format CitySize column to 4 digits (pad with zeros)
        firebird_df['CITYSIZE'] = firebird_df['CITYSIZE'].astype(str).str.zfill(4)

        print(firebird_df.head())

        firebird_df.to_excel('firebird_data.xlsx', index=False)
        print(f"Successfully retrieved {len(firebird_df)} records from Firebird databases")

        # Close Firebird connections
        tam_conn.close()
        mod_conn.close()

    except Exception as e:
        print(f"Firebird connection failed: {e}")
        print("Creating sample Firebird data for testing...")
        # Create sample data for testing
        firebird_df = pd.DataFrame({
            'panel': ['1001', '1002', '1003', '1004', '1005'],
            'surname': ['Smith', 'Johnson', 'Williams', 'Brown', 'Jones'],
            'citysize': ['1', '2', '3', '1', '2'],
            'source': ['TAM', 'TAM', 'MOD', 'MOD', 'TAM']
        })

        # Format PANEL column to 10 digits (pad with zeros)
        firebird_df['panel'] = firebird_df['panel'].astype(str).str.zfill(10)

        # Format CitySize column to 4 digits (pad with zeros)
        firebird_df['citysize'] = firebird_df['citysize'].astype(str).str.zfill(4)

        print(f"Created sample data with {len(firebird_df)} records")

    try:
        # MySQL connection
        print("Attempting to connect to MySQL database...")
        mysql_conn = pymysql.connect(
            host="*************",
            user="root",
            password="22318430",
            database="order"
        )

        # Get all active maintainers
        maintainer_sql = "SELECT mainname FROM maintain WHERE Active = 1"
        maintainers_df = pd.read_sql(maintainer_sql, mysql_conn)
        active_maintainers = maintainers_df['mainname'].tolist()
        print(f"Found {len(active_maintainers)} active maintainers: {active_maintainers}")

        # Get all order data (we'll filter by maintainer later)
        sql2 = "select * from torder where eng ='MAIN-順查' and F_date is null"
        mysql_df2 = pd.read_sql(sql2, mysql_conn)
        mysql_df2.to_excel('mysql_data2.xlsx', index=False)
        print(f"Retrieved {len(mysql_df2)} total orders")

        # Close MySQL connection
        mysql_conn.close()

    except Exception as e:
        print(f"MySQL connection failed: {e}")
        print("Creating sample data for testing...")
        # Create sample data for testing
        active_maintainers = ['Sue', 'John', 'Mary']
        mysql_df2 = pd.DataFrame({
            'Panel_No': ['0000001001', '0000001002', '0000001006'],
            'Type': ['A', 'B', 'A'],
            'I_Date': ['2024-01-15', '2024-01-16', '2024-01-17'],
            'Memo': ['Test memo 1', 'Test memo 2', 'Test memo 3'],
            'eng': ['MAIN-順查', 'MAIN-順查', 'MAIN-順查'],
            'F_date': [None, None, None]
        })
        print(f"Created sample data: {len(active_maintainers)} maintainers, {len(mysql_df2)} orders")

    # Process each active maintainer separately
    for maintainer in active_maintainers:
        print(f"\n=== Processing maintainer: {maintainer} ===")

        try:
            # Connect to MySQL for each maintainer to get their region codes
            mysql_conn = pymysql.connect(
                host="*************",
                user="root",
                password="22318430",
                database="order"
            )

            # Get region codes for current maintainer and month
            sql1 = f"select A.regioncode from mainarea A,maintain B where B.mainname='{maintainer}' and A.maintainer=B.maincode and A.month1={thismonth}"
            mysql_df1 = pd.read_sql(sql1, mysql_conn)
            mysql_conn.close()

            print(f"Found {len(mysql_df1)} region codes for {maintainer}")

        except Exception as e:
            print(f"Failed to get region codes for {maintainer}: {e}")
            # Use sample data for testing
            mysql_df1 = pd.DataFrame({'regioncode': ['0001', '0002', '0003']})

        # Filter: match citysize with regioncode for this maintainer
        if firebird_df is not None and not mysql_df1.empty:
            filtered_df = firebird_df[firebird_df['CITYSIZE'].isin(mysql_df1['regioncode'])]
            print(f"Filtered data for {maintainer}: {len(filtered_df)} records match the criteria")
        else:
            filtered_df = pd.DataFrame()
            print(f"No data available for filtering for {maintainer}")

        # Additional filtering: match PANEL from filtered_df with Panel_No from mysql_df2
        final_filtered_df = pd.DataFrame()
        if not filtered_df.empty and mysql_df2 is not None and not mysql_df2.empty:
            # Inner join on PANEL = Panel_No, only keep records that exist in both datasets
            merged_df = pd.merge(
                filtered_df[['PANEL', 'SURNAME']],
                mysql_df2[['Panel_No', 'Type', 'I_Date', 'Memo']],
                left_on='PANEL',
                right_on='Panel_No',
                how='inner'
            )

            # Select only the required columns
            final_filtered_df = merged_df[['Panel_No', 'Type', 'I_Date', 'Memo', 'SURNAME']]

            print(f"Final filtered data for {maintainer}: {len(final_filtered_df)} records matched")
            print(f"Matched panels for {maintainer}: {final_filtered_df['Panel_No'].nunique()} unique panels")
        else:
            print(f"No matching data for {maintainer}")
            final_filtered_df = pd.DataFrame()

        # Export to Excel with required columns for this maintainer
        try:
            if not final_filtered_df.empty:
                from openpyxl import Workbook
                from openpyxl.styles import Font, Alignment

                # Create a new workbook
                wb = Workbook()
                ws = wb.active

                # Define required headers
                headers = ['禮券月份', '服務專員', 'Panel', '姓名', '下單日期', '類別', '說明', '回覆']

                # Set headers
                for col_idx, header_value in enumerate(headers, start=1):
                    cell = ws.cell(row=1, column=col_idx, value=header_value)
                    # Apply header formatting
                    cell.font = Font(bold=True)
                    cell.alignment = Alignment(horizontal='center')

                # Get current month category for 禮券月份
                current_month = datetime.now().strftime('%m')
                month_cat = month_category(current_month)
                if month_cat == 1:
                    voucher_month = "1-4-7-10"
                elif month_cat == 2:
                    voucher_month = "2-5-8-11"
                else:
                    voucher_month = "3-6-9-12"

                # Write data starting from row 2
                for idx, (_, row) in enumerate(final_filtered_df.iterrows(), start=2):
                    ws.cell(row=idx, column=1, value=voucher_month)  # 禮券月份
                    ws.cell(row=idx, column=2, value=maintainer)  # 服務專員 (use current maintainer)
                    ws.cell(row=idx, column=3, value=str(row['Panel_No']).lstrip('0'))  # Panel (remove leading zeros)
                    ws.cell(row=idx, column=4, value=row['SURNAME'])  # 姓名
                    ws.cell(row=idx, column=5, value=row['I_Date'])  # 下單日期
                    ws.cell(row=idx, column=6, value=row['Type'])  # 類別
                    ws.cell(row=idx, column=7, value=row['Memo'])  # 說明
                    ws.cell(row=idx, column=8, value='')  # 回覆 (empty)

                # Set specific column widths
                column_widths = {
                    'A': 12,  # 禮券月份
                    'B': 10,  # 服務專員
                    'C': 12,  # Panel
                    'D': 10,  # 姓名
                    'E': 12,  # 下單日期
                    'F': 15,  # 類別
                    'G': 60,  # 說明 (wider for long text)
                    'H': 20   # 回覆
                }

                for col_letter, width in column_widths.items():
                    ws.column_dimensions[col_letter].width = width

                # Apply formatting to all data cells (starting from row 2)
                for row_idx in range(2, len(final_filtered_df) + 2):
                    for col_idx in range(1, 9):  # 8 columns
                        cell = ws.cell(row=row_idx, column=col_idx)

                        # Special formatting for 說明 column (column G, index 7)
                        if col_idx == 7:  # 說明欄位
                            cell.alignment = Alignment(
                                vertical='top',
                                horizontal='left',
                                wrap_text=True  # 自動換列
                            )
                        else:
                            # General alignment for other cells
                            cell.alignment = Alignment(vertical='center', horizontal='center')

                # Save the workbook with maintainer name to configured path
                filename = f"{maintainer}.xlsx"
                full_path = os.path.join(output_path,voucher_month, filename)
                wb.save(full_path)
                print(f"Report exported to {full_path} with {len(final_filtered_df)} records")
            else:
                print(f"No data to export for {maintainer}")

        except Exception as e:
            print(f"Error exporting Excel for {maintainer}: {e}")

    print(f"\n=== Processing completed for all {len(active_maintainers)} maintainers ===")

if __name__ == "__main__":
    main()




