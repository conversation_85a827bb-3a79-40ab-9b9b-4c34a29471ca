# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: firebird/base/config.proto
# Protobuf Python Version: 4.25.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import any_pb2 as google_dot_protobuf_dot_any__pb2
from google.protobuf import struct_pb2 as google_dot_protobuf_dot_struct__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x1a\x66irebird/base/config.proto\x12\rfirebird.base\x1a\x19google/protobuf/any.proto\x1a\x1cgoogle/protobuf/struct.proto\"\xf0\x01\n\x05Value\x12\x13\n\tas_string\x18\x02 \x01(\tH\x00\x12\x12\n\x08\x61s_bytes\x18\x03 \x01(\x0cH\x00\x12\x11\n\x07\x61s_bool\x18\x04 \x01(\x08H\x00\x12\x13\n\tas_double\x18\x05 \x01(\x01H\x00\x12\x12\n\x08\x61s_float\x18\x06 \x01(\x02H\x00\x12\x13\n\tas_sint32\x18\x07 \x01(\x11H\x00\x12\x13\n\tas_sint64\x18\x08 \x01(\x12H\x00\x12\x13\n\tas_uint32\x18\t \x01(\rH\x00\x12\x13\n\tas_uint64\x18\n \x01(\x04H\x00\x12&\n\x06\x61s_msg\x18\x0b \x01(\x0b\x32\x14.google.protobuf.AnyH\x00\x42\x06\n\x04kind\"\x93\x02\n\x0b\x43onfigProto\x12\x38\n\x07options\x18\x01 \x03(\x0b\x32\'.firebird.base.ConfigProto.OptionsEntry\x12\x38\n\x07\x63onfigs\x18\x02 \x03(\x0b\x32\'.firebird.base.ConfigProto.ConfigsEntry\x1a\x44\n\x0cOptionsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12#\n\x05value\x18\x02 \x01(\x0b\x32\x14.firebird.base.Value:\x02\x38\x01\x1aJ\n\x0c\x43onfigsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12)\n\x05value\x18\x02 \x01(\x0b\x32\x1a.firebird.base.ConfigProto:\x02\x38\x01\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'firebird.base.config_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  _globals['_CONFIGPROTO_OPTIONSENTRY']._options = None
  _globals['_CONFIGPROTO_OPTIONSENTRY']._serialized_options = b'8\001'
  _globals['_CONFIGPROTO_CONFIGSENTRY']._options = None
  _globals['_CONFIGPROTO_CONFIGSENTRY']._serialized_options = b'8\001'
  _globals['_VALUE']._serialized_start=103
  _globals['_VALUE']._serialized_end=343
  _globals['_CONFIGPROTO']._serialized_start=346
  _globals['_CONFIGPROTO']._serialized_end=621
  _globals['_CONFIGPROTO_OPTIONSENTRY']._serialized_start=477
  _globals['_CONFIGPROTO_OPTIONSENTRY']._serialized_end=545
  _globals['_CONFIGPROTO_CONFIGSENTRY']._serialized_start=547
  _globals['_CONFIGPROTO_CONFIGSENTRY']._serialized_end=621
# @@protoc_insertion_point(module_scope)
